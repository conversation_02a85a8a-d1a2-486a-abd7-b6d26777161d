# PGM与PCD方向对齐修复

## 问题分析

从用户提供的图片可以清楚看到：
- **PCD点云**（白色部分）和**PGM地图**（绿色部分）形状相似
- **但方向完全相反**，说明其中一个在坐标轴上是翻转的
- 需要确定哪个是正确的，并修复另一个

## 修复策略

### 1. 逻辑分析
- **PGM文件**：来自YAML配置，有明确的世界坐标原点`[-11.4, -8.63, 0]`
- **PCD文件**：从PGM生成的对应点云
- **结论**：PGM作为原始地图数据应该是正确的，PCD可能在生成过程中有坐标转换问题

### 2. 修复方案
同时调整PGM和PCD的方向设置，找到最佳匹配：

#### PCD坐标修复
```javascript
// 修复前
z = point.y         // PCD的Y轴对应Three.js的Z轴

// 修复后
z = -point.y        // PCD的Y轴翻转后对应Three.js的Z轴
```

#### PGM纹理修复
```javascript
// 修复前
texture.flipY = true  // Y轴翻转

// 修复后
texture.flipY = false // 不翻转，尝试与PCD对应
```

## 技术细节

### PCD坐标转换
```javascript
// 新的坐标映射
x = point.x         // X轴保持一致
y = point.z         // PCD的Z轴对应Three.js的Y轴（高度）
z = -point.y        // PCD的Y轴翻转后对应Three.js的Z轴（深度）
```

### PGM纹理设置
```javascript
// 两个地方都需要修改
// 1. createPGMTextureWithConfig方法
texture.flipY = false

// 2. createPGMTexture方法  
texture.flipY = false
```

### 边界计算同步
```javascript
// PCD边界计算也需要同步修改
const z = -point.y    // 与渲染保持一致
```

## 预期效果

### 修复后应该看到：
1. **PCD点云**和**PGM地图**在相同位置
2. **相同的建筑物**在两种模式下方向一致
3. **地图特征**（道路、建筑等）完美重叠
4. **位置元素**在两种模式下位置完全相同

### 如果还是不对：
可以使用之前添加的方向测试按钮：
- **翻转X**: 测试X轴翻转
- **翻转Y**: 测试Y轴翻转  
- **翻转Z**: 测试Z轴翻转

## 测试步骤

### 1. 清除当前渲染
```javascript
// 清除PGM和PCD
this.clearPGM()
this.clearPCD()
```

### 2. 重新渲染测试
```javascript
// 1. 先渲染PGM
await this.renderPGM()

// 2. 再渲染PCD
await this.renderPCD()

// 3. 观察是否对齐
```

### 3. 验证对齐
- 查看建筑物是否在相同位置
- 查看道路走向是否一致
- 放置位置元素验证坐标对应

## 可能的组合

如果当前修复不完美，可能需要尝试以下组合：

### 组合1（当前）
- PGM: `flipY = false`
- PCD: `z = -point.y`

### 组合2
- PGM: `flipY = true`  
- PCD: `z = -point.y`

### 组合3
- PGM: `flipY = false`
- PCD: `z = point.y`

### 组合4
- PGM: `flipY = true`
- PCD: `z = point.y`

## 调试信息

### 控制台输出
```
PGM边界（世界坐标）: {
  minX: -11.4, maxX: 4.6,
  minZ: -8.63, maxZ: 3.37,
  centerX: -3.4, centerZ: -2.63
}

PCD边界（3D）: {
  minX: -11.4, maxX: 4.6,
  minZ: -3.37, maxZ: 8.63,  // 注意Z轴范围变化
  centerX: -3.4, centerZ: 2.63
}
```

### 判断标准
- **X轴范围**应该相同
- **Z轴中心**应该相同（或相反但对称）
- **整体形状**应该匹配

## 最终目标

实现PGM（2D地图）和PCD（3D点云）的完美对齐：
- ✅ **空间位置一致**
- ✅ **方向朝向一致**  
- ✅ **特征对应一致**
- ✅ **坐标系统一**

现在请测试新的渲染效果，看看PGM和PCD是否对齐了！
