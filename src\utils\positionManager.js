/**
 * 位置管理模块
 * 处理位置点的创建、更新、删除等操作
 */

import * as THREE from 'three'

/**
 * 位置管理器类
 */
export class PositionManager {
  constructor() {
    this.positionCounters = {}
    this.nextId = 1
  }

  /**
   * 获取当前地图的位置计数
   */
  getPositionCountForMap(mapId) {
    if (!this.positionCounters[mapId]) {
      this.positionCounters[mapId] = 1
    }
    return this.positionCounters[mapId]++
  }

  /**
   * 设置位置计数器
   */
  setPositionCounter(mapId, count) {
    this.positionCounters[mapId] = count
  }

  /**
   * 创建位置数据
   */
  createPositionData(targetMapId, position) {
    // 获取位置计数和名称
    const positionCount = this.getPositionCountForMap(targetMapId)
    const name = `位置${positionCount}`
    const id = this.nextId++ // 用于3D对象的内部ID
    const uniqueId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}` // 唯一标识符

    // 创建位置数据（新建时ID为空）
    const positionData = {
      id: '',
      name: name,
      xcoordinate: position.x.toString(),
      ycoordinate: position.y.toString(),
      zcoordinate: position.z.toString(),
      yaw: '0',
      actionNo: '',
      contentDetail: '',
      uniqueId: uniqueId // 添加唯一标识符
    }

    return {
      positionData,
      objectData: {
        id: id, // 传递3D对象的内部ID
        name: name,
        actionNo: positionData.actionNo,
        contentDetail: positionData.contentDetail,
        rotation: 0,
        uniqueId: uniqueId // 传递唯一标识符
      }
    }
  }

  /**
   * 更新位置属性
   */
  updatePositionProperty(selectedPositionData, property, value) {
    if (!selectedPositionData) return null

    // 对于数字类型的属性，确保转换为字符串存储
    if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
      selectedPositionData[property] = String(value || 0)
    } else {
      selectedPositionData[property] = value
    }

    return selectedPositionData
  }

  /**
   * 从3D对象设置位置数据
   */
  setPositionDataFromObject(object) {
    if (!object) {
      return null
    }

    // 检查是否是新建的元素
    const isNewElement = this.isNewElement(object)

    // 将3D对象数据转换为位置数据格式
    return {
      id: isNewElement ? '' : (object.businessId || ''), // 新建元素ID为空，已保存元素使用businessId
      name: object.name || '未命名位置',
      xcoordinate: (object.position?.x || 0).toString(),
      ycoordinate: (object.position?.y || 0).toString(),
      zcoordinate: (object.position?.z || 0).toString(),
      yaw: object.getRotationDegrees ? object.getRotationDegrees().toString() : '0',
      actionNo: object.actionNo || '',
      contentDetail: object.contentDetail || '',
      uniqueId: object.uniqueId || null // 保存唯一标识符
    }
  }

  /**
   * 检查是否是新建的元素
   */
  isNewElement(object) {
    // 简单判断：有uniqueId且没有businessId的是新建元素
    return object.uniqueId && !object.businessId
  }

  /**
   * 根据位置数据更新3D场景中的对象
   */
  updateSceneObjectFromPositionData(selectedPositionData, selectedObject, property, value) {
    if (!selectedPositionData || !selectedObject) return false

    // 直接使用当前选中的3D对象
    const sceneObject = selectedObject

    if (sceneObject) {
      // 更新名称
      if (property === 'name') {
        sceneObject.name = value
      }

      // 更新位置坐标
      if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
        const newPosition = {
          x: parseFloat(selectedPositionData.xcoordinate) || 0,
          y: parseFloat(selectedPositionData.ycoordinate) || 0,
          z: parseFloat(selectedPositionData.zcoordinate) || 0
        }

        sceneObject.position.copy(newPosition)
        if (sceneObject.mesh) {
          sceneObject.mesh.position.copy(newPosition)
        }
      }

      // 更新旋转
      if (property === 'yaw') {
        const newRotation = (parseFloat(selectedPositionData.yaw) || 0) * Math.PI / 180
        if (sceneObject.setRotation) {
          sceneObject.setRotation(newRotation)
        }
      }

      // 更新业务属性
      if (property === 'actionNo') {
        sceneObject.actionNo = value || ''
      }

      if (property === 'contentDetail') {
        sceneObject.contentDetail = value || ''
      }

      console.log(`已更新3D对象属性: ${property} = ${value}`)
      return true
    }
    return false
  }

  /**
   * 计算射线与地面的交点
   */
  calculateGroundIntersection(event, threeContainer, camera, pgmRenderer = null) {
    const rect = threeContainer.getBoundingClientRect()
    const mouse = new THREE.Vector2()
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    const raycaster = new THREE.Raycaster()
    raycaster.setFromCamera(mouse, camera)

    // 与地面相交
    const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0)
    const intersectPoint = new THREE.Vector3()

    // 检查射线与平面的交点
    const intersection = raycaster.ray.intersectPlane(plane, intersectPoint)

    // 如果没有交点，使用默认位置
    if (!intersection) {
      console.warn('No intersection with ground plane, using default position')
      intersectPoint.set(0, 0, 0)
    }

    // 验证intersectPoint是有效的
    if (!intersectPoint || typeof intersectPoint.x !== 'number' || isNaN(intersectPoint.x)) {
      console.warn('Invalid intersect point, using default position')
      intersectPoint.set(0, 0, 0)
    }

    // 如果有PGM渲染器且已渲染PGM，限制点在PGM范围内
    if (pgmRenderer && pgmRenderer.hasPGM()) {
      const clampedPoint = pgmRenderer.clampPointToPGMBounds(intersectPoint.x, intersectPoint.z)
      intersectPoint.x = clampedPoint.x
      intersectPoint.z = clampedPoint.z
      console.log('Position clamped to PGM bounds:', clampedPoint)
    }

    return intersectPoint
  }

  /**
   * 处理旋转操作
   */
  handleRotation(selectedPositionData, rotationDelta) {
    if (!selectedPositionData) return null

    const currentYaw = parseFloat(selectedPositionData.yaw) || 0
    let newYaw = currentYaw + rotationDelta

    // 保持角度在0-360范围内
    if (newYaw < 0) newYaw += 360
    if (newYaw >= 360) newYaw -= 360

    return newYaw
  }

  /**
   * 设置nextId
   */
  setNextId(id) {
    this.nextId = id
  }

  /**
   * 获取nextId
   */
  getNextId() {
    return this.nextId
  }

  /**
   * 增加nextId
   */
  incrementNextId() {
    return this.nextId++
  }

  /**
   * 验证并限制位置在PGM范围内
   */
  validateAndClampPosition(position, pgmRenderer) {
    if (!pgmRenderer || !pgmRenderer.hasPGM()) {
      return position // 如果没有PGM，返回原位置
    }

    const clampedPoint = pgmRenderer.clampPointToPGMBounds(position.x, position.z)
    return {
      x: clampedPoint.x,
      y: position.y, // Y坐标保持不变
      z: clampedPoint.z
    }
  }

  /**
   * 检查位置是否在PGM范围内
   */
  isPositionInPGMBounds(position, pgmRenderer) {
    if (!pgmRenderer || !pgmRenderer.hasPGM()) {
      return true // 如果没有PGM，认为所有位置都有效
    }

    return pgmRenderer.isPointInPGMBounds(position.x, position.z)
  }
}
