/**
 * PCD文件渲染工具模块
 * 处理PCD文件的加载、解析和渲染
 */

import * as THREE from 'three'
import { markRaw } from 'vue'

/**
 * PCD渲染器类
 */
export class PCDRenderer {
  constructor(scene) {
    this.scene = scene
    this.pcdMesh = null
    this.pcdData = null // 存储PCD数据信息
    this.pcdBounds = null // 存储PCD边界信息
  }

  /**
   * 检查PCD文件格式
   */
  async checkPCDFile(filePath) {
    try {
      const response = await fetch(filePath)
      if (!response.ok) {
        throw new Error(`无法加载PCD文件: ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)

      console.log('文件大小:', arrayBuffer.byteLength, '字节')

      // 显示文件前500字节的内容
      const firstBytes = Array.from(uint8Array.slice(0, 500))
        .map(b => b >= 32 && b <= 126 ? String.fromCharCode(b) : `[${b}]`)
        .join('')

      console.log('文件前500字节内容:')
      console.log(firstBytes)

      // 查找DATA行
      const decoder = new TextDecoder('utf-8', { fatal: false })
      const textPortion = decoder.decode(uint8Array.slice(0, Math.min(uint8Array.length, 2000)))
      const lines = textPortion.split(/[\r\n]+/)

      console.log('文件前20行:')
      lines.slice(0, 20).forEach((line, index) => {
        console.log(`${index + 1}: ${line}`)
      })

      return { success: true, lines: lines.slice(0, 20) }
    } catch (error) {
      console.error('检查PCD文件失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 渲染PCD文件
   */
  async renderPCD(filePath = '/assets/screen.pcd', scale = 1.0, pointSize = 0.05) {
    try {
      // 首先检查文件格式
      console.log('开始检查PCD文件格式...')
      await this.checkPCDFile(filePath)

      // 移除之前的PCD网格
      this.removePreviousPCD()

      // 加载PCD文件
      console.log('开始解析PCD文件...')
      const pcdData = await this.loadPCDFile(filePath)
      this.pcdData = pcdData

      // 创建PCD点云
      this.pcdMesh = this.createPCDPointCloud(pcdData, scale, pointSize)

      // 计算PCD边界信息
      this.calculatePCDBounds(pcdData, scale)

      // 添加到场景
      this.scene.add(this.pcdMesh)

      console.log('PCD文件渲染完成:', pcdData)
      console.log('PCD边界信息:', this.pcdBounds)
      return {
        success: true,
        data: pcdData,
        bounds: this.pcdBounds,
        mesh: this.pcdMesh
      }
    } catch (error) {
      console.error('PCD渲染错误:', error)
      throw error
    }
  }

  /**
   * 移除之前的PCD网格
   */
  removePreviousPCD() {
    if (this.pcdMesh) {
      this.scene.remove(this.pcdMesh)
      // 清理资源
      if (this.pcdMesh.geometry) {
        this.pcdMesh.geometry.dispose()
      }
      if (this.pcdMesh.material) {
        this.pcdMesh.material.dispose()
      }
      this.pcdMesh = null
    }
  }

  /**
   * 加载PCD文件
   */
  async loadPCDFile(filePath) {
    const response = await fetch(filePath)
    if (!response.ok) {
      throw new Error(`无法加载PCD文件: ${response.status}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    return this.parsePCD(arrayBuffer)
  }

  /**
   * 解析PCD文件
   */
  parsePCD(arrayBuffer) {
    // 将ArrayBuffer转换为字符串来解析头部
    const uint8Array = new Uint8Array(arrayBuffer)

    // 首先尝试将整个文件转换为文本来查找头部
    let headerText = ''
    let headerEnd = 0
    let foundDataLine = false

    // 方法1: 逐字节读取直到找到DATA行
    const maxHeaderSize = Math.min(uint8Array.length, 50000) // 增加到50KB

    for (let i = 0; i < maxHeaderSize; i++) {
      const byte = uint8Array[i]

      // 跳过非ASCII字符（可能是二进制数据的开始）
      if (byte > 127) {
        // 如果遇到非ASCII字符，检查之前是否已经有完整的头部
        const lines = headerText.split('\n')
        for (let j = 0; j < lines.length; j++) {
          if (lines[j].trim().startsWith('DATA ')) {
            foundDataLine = true
            headerEnd = headerText.lastIndexOf(lines[j]) + lines[j].length + 1
            break
          }
        }
        if (foundDataLine) break

        // 如果没有找到DATA行但遇到二进制数据，可能是格式问题
        console.warn('在位置', i, '遇到非ASCII字符，尝试其他解析方法')
        break
      }

      const char = String.fromCharCode(byte)
      headerText += char

      // 检查是否完成了一行
      if (byte === 10 || byte === 13) { // 换行符 LF 或 CR
        const lines = headerText.split(/[\r\n]+/)
        const lastLine = lines[lines.length - 2] // 倒数第二行（最后一行可能是空的）

        if (lastLine && lastLine.trim().startsWith('DATA ')) {
          foundDataLine = true
          headerEnd = i + 1
          break
        }
      }
    }

    // 方法2: 如果方法1失败，尝试将前面部分转换为文本
    if (!foundDataLine) {
      console.log('方法1失败，尝试方法2解析头部')

      // 尝试解码前面的字节为UTF-8文本
      const decoder = new TextDecoder('utf-8', { fatal: false })
      const textPortion = decoder.decode(uint8Array.slice(0, Math.min(uint8Array.length, 10000)))

      const lines = textPortion.split(/[\r\n]+/)
      let dataLineIndex = -1

      for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim().startsWith('DATA ')) {
          dataLineIndex = i
          foundDataLine = true
          break
        }
      }

      if (foundDataLine) {
        headerText = lines.slice(0, dataLineIndex + 1).join('\n') + '\n'
        // 计算headerEnd位置
        const headerBytes = new TextEncoder().encode(headerText)
        headerEnd = headerBytes.length
      }
    }

    // 方法3: 最后的尝试 - 查找"DATA"字符串
    if (!foundDataLine) {
      console.log('方法2失败，尝试方法3查找DATA字符串')

      const dataPattern = new Uint8Array([68, 65, 84, 65, 32]) // "DATA "的ASCII码

      for (let i = 0; i <= uint8Array.length - dataPattern.length; i++) {
        let match = true
        for (let j = 0; j < dataPattern.length; j++) {
          if (uint8Array[i + j] !== dataPattern[j]) {
            match = false
            break
          }
        }

        if (match) {
          // 找到"DATA "，向前查找行的开始
          let lineStart = i
          while (lineStart > 0 && uint8Array[lineStart - 1] !== 10 && uint8Array[lineStart - 1] !== 13) {
            lineStart--
          }

          // 向后查找行的结束
          let lineEnd = i + dataPattern.length
          while (lineEnd < uint8Array.length && uint8Array[lineEnd] !== 10 && uint8Array[lineEnd] !== 13) {
            lineEnd++
          }

          // 提取头部
          headerText = new TextDecoder().decode(uint8Array.slice(0, lineEnd + 1))
          headerEnd = lineEnd + 1
          foundDataLine = true
          break
        }
      }
    }

    if (!foundDataLine) {
      // 输出调试信息
      const firstBytes = Array.from(uint8Array.slice(0, 100)).map(b => String.fromCharCode(b)).join('')
      console.error('无法找到DATA行，文件前100字节:', firstBytes)
      throw new Error('无法找到PCD文件的DATA行，可能文件格式不正确')
    }

    console.log('找到DATA行，头部长度:', headerEnd, '字节')
    console.log('头部内容预览:', headerText.substring(0, 200))

    const lines = headerText.split(/[\r\n]+/)
    const header = {}

    // 解析头部信息
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // 检查DATA行
      if (line.startsWith('DATA ')) {
        const dataParts = line.split(/\s+/)
        if (dataParts.length >= 2) {
          header.dataType = dataParts[1].toLowerCase()
          console.log('检测到数据格式:', header.dataType)
        }
        break
      }

      if (line.startsWith('#') || line === '') continue // 跳过注释和空行

      const parts = line.split(/\s+/) // 使用正则表达式分割，处理多个空格
      if (parts.length >= 2) {
        const key = parts[0].toLowerCase()
        try {
          switch (key) {
            case 'version':
              header.version = parts[1]
              console.log('PCD版本:', header.version)
              break
            case 'fields':
              header.fields = parts.slice(1)
              console.log('字段:', header.fields)
              break
            case 'size':
              header.size = parts.slice(1).map(Number)
              console.log('字段大小:', header.size)
              break
            case 'type':
              header.type = parts.slice(1)
              console.log('字段类型:', header.type)
              break
            case 'count':
              header.count = parts.slice(1).map(Number)
              console.log('字段计数:', header.count)
              break
            case 'width':
              header.width = parseInt(parts[1])
              console.log('宽度:', header.width)
              break
            case 'height':
              header.height = parseInt(parts[1])
              console.log('高度:', header.height)
              break
            case 'viewpoint':
              header.viewpoint = parts.slice(1).map(Number)
              console.log('视点:', header.viewpoint)
              break
            case 'points':
              header.points = parseInt(parts[1])
              console.log('点数:', header.points)
              break
          }
        } catch (e) {
          console.warn(`解析头部字段 ${key} 时出错:`, e)
        }
      }
    }

    // 验证必要的头部信息
    if (!header.dataType) {
      throw new Error('未找到DATA行或数据类型')
    }

    if (!header.fields || header.fields.length === 0) {
      console.warn('未找到FIELDS信息，使用默认字段 [x, y, z]')
      header.fields = ['x', 'y', 'z']
    }

    if (!header.points || header.points <= 0) {
      console.warn('未找到有效的POINTS信息')
    }

    // 解析点云数据
    const points = []

    try {
      if (header.dataType === 'ascii') {
        // ASCII格式解析
        const dataStartIndex = headerText.indexOf('DATA ascii')
        if (dataStartIndex === -1) {
          throw new Error('无法找到ASCII数据开始位置')
        }

        const dataText = headerText.substring(dataStartIndex + 10)
        const dataLines = dataText.split('\n')

        console.log(`开始解析ASCII数据，共 ${dataLines.length} 行`)

        // 限制处理的行数，防止栈溢出
        const maxLines = Math.min(dataLines.length, 50000) // 最多处理5万行
        let validPointCount = 0

        for (let i = 0; i < maxLines; i++) {
          const line = dataLines[i].trim()
          if (line === '') continue

          try {
            const values = line.split(/\s+/)
            if (values.length >= 3) {
              const x = parseFloat(values[0])
              const y = parseFloat(values[1])
              const z = parseFloat(values[2])

              // 验证数值有效性
              if (isFinite(x) && isFinite(y) && isFinite(z)) {
                const point = { x, y, z }

                // 如果有颜色信息
                if (header.fields && (header.fields.includes('rgb') || header.fields.includes('rgba'))) {
                  const colorIndex = header.fields.indexOf('rgb') !== -1 ?
                                    header.fields.indexOf('rgb') :
                                    header.fields.indexOf('rgba')
                  if (values[colorIndex] !== undefined) {
                    const colorValue = parseFloat(values[colorIndex])
                    if (isFinite(colorValue)) {
                      point.color = this.parseRGBValue(colorValue)
                    }
                  }
                }

                points.push(point)
                validPointCount++

                // 每1000个点输出一次进度
                if (validPointCount % 1000 === 0) {
                  console.log(`已解析 ${validPointCount} 个有效点`)
                }
              }
            }
          } catch (lineError) {
            console.warn(`解析第 ${i} 行时出错:`, lineError)
            continue
          }
        }

        console.log(`ASCII解析完成，共解析 ${validPointCount} 个有效点`)

      } else if (header.dataType === 'binary') {
        // 二进制格式解析
        console.log('开始解析二进制数据')
        const binaryPoints = this.parseBinaryPCDData(uint8Array, headerEnd, header)

        // 分批添加点，避免栈溢出
        const batchSize = 10000
        for (let i = 0; i < binaryPoints.length; i += batchSize) {
          const batch = binaryPoints.slice(i, i + batchSize)
          points.push(...batch)

          if (i % 50000 === 0) {
            console.log(`已添加 ${Math.min(i + batchSize, binaryPoints.length)} / ${binaryPoints.length} 个点`)
          }
        }

        console.log(`二进制解析完成，共解析 ${points.length} 个点`)

      } else {
        throw new Error(`不支持的PCD数据格式: ${header.dataType}`)
      }
    } catch (error) {
      console.error('解析点云数据时出错:', error)
      throw new Error(`点云数据解析失败: ${error.message}`)
    }

    return {
      header,
      points,
      pointCount: points.length
    }
  }

  /**
   * 解析二进制PCD数据
   */
  parseBinaryPCDData(uint8Array, headerEnd, header) {
    const points = []

    try {
      // 检查数据是否足够
      if (headerEnd >= uint8Array.length) {
        throw new Error('头部结束位置超出文件大小')
      }

      const dataView = new DataView(uint8Array.buffer, headerEnd)
      const availableBytes = uint8Array.length - headerEnd

      // 计算每个点的字节大小
      let pointSize = 16 // 默认值：x,y,z,rgb 各4字节

      if (header.size && header.fields && header.size.length === header.fields.length) {
        pointSize = 0
        for (let i = 0; i < header.fields.length; i++) {
          const fieldSize = header.size[i] * (header.count ? header.count[i] : 1)
          pointSize += fieldSize
        }
      }

      // 计算实际可解析的点数
      const maxPointCount = Math.floor(availableBytes / pointSize)
      const targetPointCount = header.points ? Math.min(header.points, maxPointCount) : maxPointCount

      console.log(`开始解析二进制PCD数据: 点大小=${pointSize}字节, 目标点数=${targetPointCount}, 可用字节=${availableBytes}`)

      // 限制最大点数防止内存溢出，并分批处理
      const maxPointsPerBatch = 10000 // 每批处理1万个点
      const actualPointCount = Math.min(targetPointCount, 500000) // 最多50万个点

      let processedPoints = 0

      // 分批处理点数据
      for (let batchStart = 0; batchStart < actualPointCount; batchStart += maxPointsPerBatch) {
        const batchEnd = Math.min(batchStart + maxPointsPerBatch, actualPointCount)
        const batchPoints = []

        for (let i = batchStart; i < batchEnd; i++) {
          const offset = i * pointSize

          // 检查边界
          if (offset + 12 > availableBytes) {
            console.warn(`到达数据边界，已解析 ${i} 个点`)
            break
          }

          try {
            const point = {
              x: dataView.getFloat32(offset, true), // little-endian
              y: dataView.getFloat32(offset + 4, true),
              z: dataView.getFloat32(offset + 8, true)
            }

            // 检查是否有RGB数据
            if (header.fields && header.fields.includes('rgb') && offset + 16 <= availableBytes) {
              try {
                const rgbValue = dataView.getUint32(offset + 12, true)
                if (rgbValue !== 0) { // 只有非零值才解析颜色
                  point.color = this.parseRGBValue(rgbValue)
                }
              } catch (e) {
                // RGB解析失败时忽略
              }
            }

            // 验证点的有效性
            if (isFinite(point.x) && isFinite(point.y) && isFinite(point.z)) {
              batchPoints.push(point)
            }
          } catch (e) {
            console.warn(`解析第${i}个点时出错:`, e)
            continue
          }
        }

        // 将批次点添加到主数组（避免大数组的spread操作）
        for (let j = 0; j < batchPoints.length; j++) {
          points.push(batchPoints[j])
        }

        processedPoints += batchPoints.length

        // 输出进度
        if (batchStart % 50000 === 0) {
          console.log(`已处理 ${processedPoints} / ${actualPointCount} 个点`)
        }
      }

      console.log(`二进制解析完成，共解析 ${points.length} 个有效点`)
      return points

    } catch (error) {
      console.error('二进制PCD数据解析失败:', error)
      throw new Error(`二进制数据解析失败: ${error.message}`)
    }
  }

  /**
   * 解析RGB值
   */
  parseRGBValue(rgbValue) {
    // PCD文件中的RGB通常是打包的32位整数
    const rgb = Math.floor(rgbValue)
    const r = (rgb >> 16) & 0xFF
    const g = (rgb >> 8) & 0xFF
    const b = rgb & 0xFF
    return { r: r / 255, g: g / 255, b: b / 255 }
  }

  /**
   * 创建PCD点云
   */
  createPCDPointCloud(pcdData, scale = 1.0, pointSize = 0.1) {
    const { points } = pcdData

    if (points.length === 0) {
      throw new Error('PCD文件中没有有效的点数据')
    }

    console.log(`开始创建点云，共 ${points.length} 个点`)

    // 限制点数防止内存溢出
    const maxPoints = Math.min(points.length, 200000) // 最多20万个点
    const actualPoints = points.slice(0, maxPoints)

    if (maxPoints < points.length) {
      console.warn(`点数过多，已限制为 ${maxPoints} 个点（原始: ${points.length}）`)
    }

    // 创建几何体
    const geometry = markRaw(new THREE.BufferGeometry())

    // 分批创建数组，避免栈溢出
    const batchSize = 10000
    const totalPoints = actualPoints.length
    const positions = new Float32Array(totalPoints * 3)
    const colors = new Float32Array(totalPoints * 3)

    console.log('开始处理点数据...')

    // 分批处理点数据
    for (let batchStart = 0; batchStart < totalPoints; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, totalPoints)

      for (let i = batchStart; i < batchEnd; i++) {
        const point = actualPoints[i]
        const index = i * 3

        // 标准的Three.js坐标系转换
        // PCD坐标系: X(右) Y(前) Z(上) → Three.js坐标系: X(右) Y(上) Z(前)
        // 如果PCD坐标很小，可能需要应用偏移来匹配PGM的世界坐标
        const x = point.x * scale
        const y = point.z * scale  // 高度
        const z = point.y * scale

        positions[index] = x
        positions[index + 1] = y
        positions[index + 2] = z

        // 设置颜色
        if (point.color) {
          colors[index] = point.color.r
          colors[index + 1] = point.color.g
          colors[index + 2] = point.color.b
        } else {
          // 默认颜色：根据高度设置渐变色
          const height = point.z * scale
          colors[index] = 0.5 + Math.min(height * 0.1, 0.5) // 红色分量
          colors[index + 1] = 0.8 // 绿色分量
          colors[index + 2] = 0.3 + Math.min(height * 0.1, 0.5) // 蓝色分量
        }
      }

      // 输出进度
      if (batchStart % 50000 === 0) {
        console.log(`已处理 ${Math.min(batchEnd, totalPoints)} / ${totalPoints} 个点`)
      }
    }

    console.log('点数据处理完成，设置几何体属性...')

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))

    // 创建材质
    const material = markRaw(new THREE.PointsMaterial({
      size: pointSize,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
      sizeAttenuation: true
    }))

    // 创建点云对象
    const pointCloud = new THREE.Points(geometry, material)

    // 添加名称标识
    pointCloud.name = 'pcd-pointcloud'
    pointCloud.userData = {
      type: 'pcd',
      pointCount: actualPoints.length,
      scale: scale,
      pointSize: pointSize
    }

    console.log(`点云创建完成，实际点数: ${actualPoints.length}`)

    // 使用markRaw防止Vue响应式代理
    return markRaw(pointCloud)
  }

  /**
   * 计算PCD边界信息
   */
  calculatePCDBounds(pcdData, scale = 1.0) {
    const { points } = pcdData
    
    if (points.length === 0) {
      this.pcdBounds = null
      return
    }
    
    let minX = Infinity, maxX = -Infinity
    let minY = Infinity, maxY = -Infinity
    let minZ = Infinity, maxZ = -Infinity

    points.forEach(point => {
      const x = point.x * scale     // X轴保持一致，应用缩放
      const y = point.z * scale     // PCD的Z轴对应Three.js的Y轴（高度）
      const z = point.y * scale     // PCD的Y轴对应Three.js的Z轴（深度）

      minX = Math.min(minX, x)
      maxX = Math.max(maxX, x)
      minY = Math.min(minY, y)
      maxY = Math.max(maxY, y)
      minZ = Math.min(minZ, z)
      maxZ = Math.max(maxZ, z)
    })

    // 3D边界信息
    this.pcdBounds = {
      minX, maxX,
      minY, maxY,
      minZ, maxZ,
      width: maxX - minX,
      height: maxY - minY,  // 实际的3D高度
      depth: maxZ - minZ,   // 实际的3D深度
      centerX: (minX + maxX) / 2,
      centerY: (minY + maxY) / 2,
      centerZ: (minZ + maxZ) / 2
    }

    console.log('PCD边界（3D）:', this.pcdBounds)
    console.log('PCD点云范围 - X:', minX.toFixed(2), '到', maxX.toFixed(2), '宽度:', (maxX - minX).toFixed(2))
    console.log('PCD点云范围 - Y:', minY.toFixed(2), '到', maxY.toFixed(2), '高度:', (maxY - minY).toFixed(2))
    console.log('PCD点云范围 - Z:', minZ.toFixed(2), '到', maxZ.toFixed(2), '深度:', (maxZ - minZ).toFixed(2))
    console.log('PCD中心点:', ((minX + maxX) / 2).toFixed(2), ((minY + maxY) / 2).toFixed(2), ((minZ + maxZ) / 2).toFixed(2))
  }

  /**
   * 获取PCD边界信息
   */
  getPCDBounds() {
    return this.pcdBounds
  }

  /**
   * 检查是否已渲染PCD
   */
  hasPCD() {
    return this.pcdMesh !== null
  }

  /**
   * 检查点是否在PCD范围内
   */
  isPointInPCDBounds(x, z) {
    if (!this.pcdBounds) return false

    return x >= this.pcdBounds.minX &&
           x <= this.pcdBounds.maxX &&
           z >= this.pcdBounds.minZ &&
           z <= this.pcdBounds.maxZ
  }

  /**
   * 将点限制在PCD范围内
   */
  clampPointToPCDBounds(x, z) {
    if (!this.pcdBounds) return { x, z }

    return {
      x: Math.max(this.pcdBounds.minX, Math.min(this.pcdBounds.maxX, x)),
      z: Math.max(this.pcdBounds.minZ, Math.min(this.pcdBounds.maxZ, z))
    }
  }

  /**
   * 更新点云材质属性
   */
  updatePointCloudMaterial(options = {}) {
    if (!this.pcdMesh || !this.pcdMesh.material) return

    const material = this.pcdMesh.material

    if (options.pointSize !== undefined) {
      material.size = options.pointSize
      this.pcdMesh.userData.pointSize = options.pointSize
    }

    if (options.opacity !== undefined) {
      material.opacity = options.opacity
    }

    if (options.transparent !== undefined) {
      material.transparent = options.transparent
    }

    material.needsUpdate = true
    console.log('点云材质已更新:', options)
  }

  /**
   * 设置点云可见性
   */
  setVisible(visible) {
    if (this.pcdMesh) {
      this.pcdMesh.visible = visible
    }
  }

  /**
   * 获取点云统计信息
   */
  getStatistics() {
    if (!this.pcdData || !this.pcdBounds) return null

    return {
      pointCount: this.pcdData.pointCount,
      bounds: this.pcdBounds,
      header: this.pcdData.header,
      hasColors: this.pcdData.points.some(p => p.color),
      memoryUsage: this.pcdData.pointCount * 6 * 4 // 位置(3) + 颜色(3) * 4字节
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    this.removePreviousPCD()
    this.pcdData = null
    this.pcdBounds = null
  }
}
