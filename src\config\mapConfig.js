/**
 * 地图配置模块
 * 包含地图数据、模拟数据和相关配置
 */

// 地图列表配置
const MAP_LIST = [
  {
    mapName: '办公区域',
    mapId: 'map001',
    mapJSON: 'xxxxxx'
  },
  {
    mapName: '操作区',
    mapId: 'map002',
    mapJSON: 'xxxxxx'
  },
]

// 模拟位置点数据
const MOCK_POSITION_DATA = {
  'map001': [
    {
      id: 'pos001',
      name: '沙盘',
      xcoordinate: '0',
      ycoordinate: '0',
      zcoordinate: '-6',
      yaw: '0',
      actionNo: 'ACT001',
      contentDetail: '欢迎来到沙盘'
    },
    {
      id: 'pos002',
      name: '数字化样板间',
      xcoordinate: '-3',
      ycoordinate: '0',
      zcoordinate: '0',
      yaw: '90',
      actionNo: 'ACT002',
      contentDetail: '欢迎来到数字化样板间'
    },
    {
      id: 'pos003',
      name: '产品墙',
      xcoordinate: '3',
      ycoordinate: '0',
      zcoordinate: '2',
      yaw: '180',
      actionNo: 'ACT003',
      contentDetail: '欢迎来到产品墙'
    }
  ],
  'map002': [
    {
      id: 'pos004',
      name: '历史墙',
      xcoordinate: '-5',
      ycoordinate: '0',
      zcoordinate: '5',
      yaw: '45',
      actionNo: 'ACT004',
      contentDetail: '欢迎来到历史墙'
    },
    {
      id: 'pos005',
      name: '前处理车间',
      xcoordinate: '5',
      ycoordinate: '0',
      zcoordinate: '5',
      yaw: '135',
      actionNo: 'ACT005',
      contentDetail: '欢迎来到前处理车间'
    },
    {
      id: 'pos006',
      name: '中央控制室',
      xcoordinate: '0',
      ycoordinate: '0',
      zcoordinate: '8',
      yaw: '180',
      actionNo: 'ACT006',
      contentDetail: '欢迎来到中央控制室'
    },
    {
      id: 'pos007',
      name: '酸奶发酵暂存缸',
      xcoordinate: '-2',
      ycoordinate: '0',
      zcoordinate: '-3',
      yaw: '270',
      actionNo: 'ACT007',
      contentDetail: '欢迎来到酸奶发酵暂存缸'
    }
  ],
}

/**
 * 地图配置类
 */
export class MapConfig {
  /**
   * 获取地图列表
   */
  static getMapList() {
    return MAP_LIST
  }

  /**
   * 根据mapId查询位置点数据（模拟API调用）
   */
  static async fetchPositionsByMapId(mapId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(MOCK_POSITION_DATA[mapId] || [])
      }, 500) // 模拟网络延迟
    })
  }

  /**
   * 将位置点数据转换为3D场景对象格式
   */
  static convertPositionDataToSceneObjects(positionData) {
    return positionData.map(pos => ({
      id: parseInt(pos.id.replace(/\D/g, '')) || Math.random() * 1000, // 提取数字ID或生成随机ID
      name: pos.name,
      type: 'person', // 默认类型为person
      position: {
        x: parseFloat(pos.xcoordinate) || 0,
        y: parseFloat(pos.ycoordinate) || 0,
        z: parseFloat(pos.zcoordinate) || 0
      },
      rotation: (parseFloat(pos.yaw) || 0) * Math.PI / 180, // 将角度转换为弧度
      actionNo: pos.actionNo, // 保留动作编号
      contentDetail: pos.contentDetail, // 保留内容详情
      businessId: pos.id // 保存原始业务ID
    }))
  }
}
