/**
 * YAML解析器
 * 用于解析PGM地图的YAML配置文件
 */

export class YAMLParser {
  /**
   * 解析YAML文件内容
   * @param {string} yamlContent - YAML文件内容
   * @returns {Object} 解析后的配置对象
   */
  static parseYAML(yamlContent) {
    const config = {}
    
    // 按行分割
    const lines = yamlContent.split('\n')
    
    for (let line of lines) {
      // 移除注释和空行
      line = line.split('#')[0].trim()
      if (!line) continue
      
      // 解析键值对
      const colonIndex = line.indexOf(':')
      if (colonIndex === -1) continue
      
      const key = line.substring(0, colonIndex).trim()
      let value = line.substring(colonIndex + 1).trim()
      
      // 处理不同类型的值
      if (value.startsWith('[') && value.endsWith(']')) {
        // 数组类型
        const arrayContent = value.slice(1, -1)
        config[key] = arrayContent.split(',').map(item => {
          const trimmed = item.trim()
          return isNaN(trimmed) ? trimmed : parseFloat(trimmed)
        })
      } else if (value === 'true' || value === 'false') {
        // 布尔类型
        config[key] = value === 'true'
      } else if (!isNaN(value) && value !== '') {
        // 数字类型
        config[key] = parseFloat(value)
      } else {
        // 字符串类型
        // 移除引号
        if ((value.startsWith('"') && value.endsWith('"')) || 
            (value.startsWith("'") && value.endsWith("'"))) {
          value = value.slice(1, -1)
        }
        config[key] = value
      }
    }
    
    return config
  }
  
  /**
   * 从URL加载并解析YAML文件
   * @param {string} yamlPath - YAML文件路径
   * @returns {Promise<Object>} 解析后的配置对象
   */
  static async loadYAML(yamlPath) {
    try {
      const response = await fetch(yamlPath)
      if (!response.ok) {
        throw new Error(`无法加载YAML文件: ${response.status}`)
      }
      
      const yamlContent = await response.text()
      return this.parseYAML(yamlContent)
    } catch (error) {
      console.error('YAML文件加载失败:', error)
      throw error
    }
  }
  
  /**
   * 验证地图配置的必要字段
   * @param {Object} config - 配置对象
   * @returns {boolean} 是否有效
   */
  static validateMapConfig(config) {
    const requiredFields = ['image', 'resolution', 'origin']
    
    for (const field of requiredFields) {
      if (!(field in config)) {
        console.warn(`缺少必要的配置字段: ${field}`)
        return false
      }
    }
    
    // 验证origin数组
    if (!Array.isArray(config.origin) || config.origin.length !== 3) {
      console.warn('origin字段必须是包含3个元素的数组 [x, y, yaw]')
      return false
    }
    
    // 验证resolution
    if (typeof config.resolution !== 'number' || config.resolution <= 0) {
      console.warn('resolution字段必须是正数')
      return false
    }
    
    return true
  }
  
  /**
   * 获取默认的地图配置
   * @returns {Object} 默认配置
   */
  static getDefaultConfig() {
    return {
      image: 'map.pgm',
      mode: 'trinary',
      resolution: 0.05,
      origin: [0, 0, 0],
      negate: 0,
      occupied_thresh: 0.65,
      free_thresh: 0.25
    }
  }
  
  /**
   * 合并配置，用默认值填充缺失的字段
   * @param {Object} config - 用户配置
   * @returns {Object} 完整的配置对象
   */
  static mergeWithDefaults(config) {
    const defaultConfig = this.getDefaultConfig()
    return { ...defaultConfig, ...config }
  }
}
