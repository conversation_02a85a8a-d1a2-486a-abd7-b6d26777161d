/**
 * 3D场景配置模块
 * 处理Three.js场景的初始化和配置
 */

import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { createNonReactiveThreeObject, makeThreeObjectNonReactive } from './threeUtils.js'

/**
 * 场景配置类
 */
export class SceneSetup {
  /**
   * 初始化Three.js场景
   */
  static initThreeJS(container) {
    const sceneConfig = {}

    // 创建场景
    sceneConfig.scene = createNonReactiveThreeObject(THREE.Scene)
    sceneConfig.scene.background = new THREE.Color(0xf0f0f0)

    // 创建相机
    sceneConfig.camera = createNonReactiveThreeObject(THREE.PerspectiveCamera,
      75, // 视角
      container.clientWidth / container.clientHeight, // 宽高比
      0.1, // 近平面
      1000 // 远平面
    )
    sceneConfig.camera.position.set(10, 10, 10)

    // 创建渲染器
    sceneConfig.renderer = createNonReactiveThreeObject(THREE.WebGLRenderer, { antialias: true })
    sceneConfig.renderer.setSize(container.clientWidth, container.clientHeight)
    sceneConfig.renderer.shadowMap.enabled = true
    sceneConfig.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    container.appendChild(sceneConfig.renderer.domElement)

    // 添加控制器
    sceneConfig.controls = makeThreeObjectNonReactive(new OrbitControls(sceneConfig.camera, sceneConfig.renderer.domElement))
    sceneConfig.controls.enableDamping = true
    sceneConfig.controls.dampingFactor = 0.05

    return sceneConfig
  }

  /**
   * 设置光源
   */
  static setupLighting(scene) {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
    scene.add(ambientLight)

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(10, 10, 5)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    scene.add(directionalLight)

    return { ambientLight, directionalLight }
  }

  /**
   * 创建地板
   */
  static createFloor(scene) {
    const geometry = new THREE.PlaneGeometry(30, 30)
    const material = new THREE.MeshLambertMaterial({
      color: 0xcccccc,
      transparent: true,
      opacity: 0.95,
      polygonOffset: true, // 启用多边形偏移，避免与网格共面闪烁
      polygonOffsetFactor: 1,
      polygonOffsetUnits: 1
    })
    const floor = new THREE.Mesh(geometry, material)
    floor.rotation.x = -Math.PI / 2
    floor.receiveShadow = true
    floor.name = 'default-floor' // 添加名称标识
    scene.add(floor)

    // 添加网格（稍微抬高，避免Z-fighting）
    const gridHelper = new THREE.GridHelper(30, 30, 0x888888, 0xaaaaaa)
    gridHelper.position.y = 0.001
    gridHelper.material.opacity = 0.4
    gridHelper.material.transparent = true
    gridHelper.name = 'default-grid' // 添加名称标识
    scene.add(gridHelper)

    // 添加坐标轴辅助器（可选，用于调试）
    const axesHelper = new THREE.AxesHelper(5)
    axesHelper.position.y = 0.002
    axesHelper.name = 'default-axes' // 添加名称标识
    scene.add(axesHelper)
    // 红色=X轴(左右), 绿色=Y轴(上下), 蓝色=Z轴(前后)

    return { floor, gridHelper, axesHelper }
  }

  /**
   * 隐藏默认地板和网格
   */
  static hideDefaultFloor(scene) {
    const floor = scene.getObjectByName('default-floor')
    const grid = scene.getObjectByName('default-grid')

    if (floor) floor.visible = false
    if (grid) grid.visible = false
  }

  /**
   * 显示默认地板和网格
   */
  static showDefaultFloor(scene) {
    const floor = scene.getObjectByName('default-floor')
    const grid = scene.getObjectByName('default-grid')

    if (floor) floor.visible = true
    if (grid) grid.visible = true
  }

  /**
   * 移除默认地板和网格
   */
  static removeDefaultFloor(scene) {
    const floor = scene.getObjectByName('default-floor')
    const grid = scene.getObjectByName('default-grid')
    const axes = scene.getObjectByName('default-axes')

    if (floor) {
      scene.remove(floor)
      if (floor.geometry) floor.geometry.dispose()
      if (floor.material) floor.material.dispose()
    }
    if (grid) {
      scene.remove(grid)
      if (grid.geometry) grid.geometry.dispose()
      if (grid.material) grid.material.dispose()
    }
    if (axes) {
      scene.remove(axes)
      if (axes.geometry) axes.geometry.dispose()
      if (axes.material) axes.material.dispose()
    }
  }

  /**
   * 设置窗口大小调整事件
   */
  static setupWindowResize(camera, renderer, container) {
    const onWindowResize = () => {
      camera.aspect = container.clientWidth / container.clientHeight
      camera.updateProjectionMatrix()
      renderer.setSize(container.clientWidth, container.clientHeight)
    }

    window.addEventListener('resize', onWindowResize)
    return onWindowResize
  }

  /**
   * 设置拖放事件
   */
  static setupDragAndDrop(container, createObjectAtPosition) {
    container.addEventListener('dragover', (e) => {
      e.preventDefault()
    })

    container.addEventListener('drop', (e) => {
      e.preventDefault()
      const objectType = e.dataTransfer.getData('text/plain')
      createObjectAtPosition(objectType, e)
    })
  }

  /**
   * 设置点击事件处理
   */
  static setupClickHandler(renderer, camera, getSceneObjects, selectObject) {
    const onCanvasClick = (event) => {
      // 将鼠标点击的屏幕坐标转换为Three.js中的标准化设备坐标
      const rect = renderer.domElement.getBoundingClientRect()
      const mouse = new THREE.Vector2()
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      // 射线投射器
      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, camera)

      // 动态获取当前的场景对象
      const sceneObjects = getSceneObjects()

      // 递归检测所有子对象，包括Group内部的mesh
      const intersects = raycaster.intersectObjects(
        sceneObjects.map(obj => obj.mesh).filter(mesh => mesh),
        true // 递归检测子对象
      )

      // 查找被点击的对象
      if (intersects.length > 0) {
        const clickedMesh = intersects[0].object
        const clickedObject = SceneSetup.findClickedObject(clickedMesh, sceneObjects)

        if (clickedObject) {
          selectObject(clickedObject)
        } else {
          selectObject(null)
        }
      } else {
        selectObject(null)
      }
    }

    renderer.domElement.addEventListener('click', onCanvasClick)
    return onCanvasClick
  }

  /**
   * 查找被点击的对象
   */
  static findClickedObject(clickedMesh, sceneObjects) {
    // 首先尝试直接匹配
    let clickedObject = sceneObjects.find(obj => obj.mesh === clickedMesh)

    // 如果没找到，可能是Group内部的子对象，向上查找
    if (!clickedObject) {
      let parent = clickedMesh.parent
      while (parent && !clickedObject) {
        clickedObject = sceneObjects.find(obj => obj.mesh === parent)
        parent = parent.parent
      }
    }

    // 还可以通过userData查找
    if (!clickedObject) {
      // 检查点击的mesh的userData
      if (clickedMesh.userData && clickedMesh.userData.objectType && clickedMesh.userData.objectId) {
        const { objectType, objectId } = clickedMesh.userData
        clickedObject = sceneObjects.find(obj =>
          obj.type === objectType && obj.id === objectId
        )
      }

      // 检查父级的userData
      if (!clickedObject && clickedMesh.parent && clickedMesh.parent.userData) {
        const { objectType, objectId } = clickedMesh.parent.userData
        if (objectType && objectId) {
          clickedObject = sceneObjects.find(obj =>
            obj.type === objectType && obj.id === objectId
          )
        }
      }
    }

    return clickedObject
  }

  /**
   * 设置滚轮事件处理
   */
  static setupWheelHandler(renderer, handleWheelRotation) {
    const onCanvasWheel = (event) => {
      return handleWheelRotation(event)
    }

    // 监听鼠标滚轮事件用于调整yaw（使用捕获阶段，优先级更高）
    renderer.domElement.addEventListener('wheel', onCanvasWheel, {
      passive: false,
      capture: true // 在捕获阶段处理，优先于OrbitControls
    })

    return onCanvasWheel
  }

  /**
   * 清空场景（不显示确认对话框）
   */
  static clearScene(scene, sceneObjects) {
    // 移除所有对象
    sceneObjects.forEach(obj => {
      scene.remove(obj.mesh)
      if (obj.dispose) {
        obj.dispose()
      }
    })

    return []
  }
}
